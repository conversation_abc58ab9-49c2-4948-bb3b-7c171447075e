import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Pressable,
  FlatList,
  RefreshControl,
  Alert,
  Dimensions,
  ActivityIndicator,
  StatusBar,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
  interpolateColor,
  useAnimatedScrollHandler,
  Easing,
} from 'react-native-reanimated';

// project services
import { comickAPI, getCoverImageUrl } from '../services/api';
import { storageService } from '../services/storageService';

const { width, height } = Dimensions.get('window');
const POSTER_CARD_WIDTH = Math.round(width * 0.36);
const POSTER_GAP = 12;

// Use a stable animated FlatList wrapper
const AnimatedFlatList = Animated.createAnimatedComponent(FlatList);

// ----------------
// Small helpers
// ----------------
const fallbackCover = 'https://via.placeholder.com/600x900/0F0F12/FFFFFF?text=No+Cover';

const LoadingScreen = () => (
  <View style={[styles.container, styles.center]}>
    <ActivityIndicator size="large" color="#A9F1B6" />
  </View>
);

// ----------------
// Carousel
// ----------------
const CarouselItem = React.memo(({ item, animatedStyle, onPress }) => {
  const uri = getCoverImageUrl(item?.md_covers?.[0]) || fallbackCover;
  return (
    <TouchableOpacity activeOpacity={0.9} style={styles.carouselCard} onPress={() => onPress?.(item)}>
      <Animated.View style={[StyleSheet.absoluteFillObject, animatedStyle]}>
        <Image
          source={{ uri }}
          style={styles.carouselImage}
          placeholder="blurhash|L6A,h2t70000_2t7IVxu00Rj?bRj"
          transition={400}
        />
      </Animated.View>

      <LinearGradient
        colors={["transparent", 'rgba(15,15,18,0.25)', 'rgba(15,15,18,0.98)']}
        style={styles.carouselGradient}
      />

      <View style={styles.carouselInfoWrap} pointerEvents="none">
        <View style={styles.tagRow}>
          <View style={styles.pillTag}><Text style={styles.pillTagText}>Featured</Text></View>
          {item?.genre ? <Text style={styles.genreText}>{item.genre}</Text> : null}
        </View>

        <Text style={styles.carouselTitle} numberOfLines={2}>{item?.title ?? 'Untitled'}</Text>
        {item?.subTitle ? <Text style={styles.carouselSubtitle}>{item.subTitle}</Text> : null}
      </View>
    </TouchableOpacity>
  );
});

const AnimatedCarouselItem = React.memo(({ item, index, scrollX, onPress }) => {
  const animatedStyle = useAnimatedStyle(() => {
    const center = scrollX.value / width;
    const offset = index - center;
    const abs = Math.min(Math.abs(offset), 2);
    const scale = 1 - abs * 0.08;
    const translateY = abs * 14;
    return { transform: [{ scale }, { translateY }] };
  });

  return <CarouselItem item={item} animatedStyle={animatedStyle} onPress={onPress} />;
});

const Dot = ({ active }) => {
  const anim = useSharedValue(active ? 1 : 0);
  useEffect(() => { anim.value = withSpring(active ? 1 : 0, { damping: 14 }); }, [active]);
  const style = useAnimatedStyle(() => ({
    width: interpolate(anim.value, [0, 1], [6, 18]),
    backgroundColor: interpolateColor(anim.value, [0, 1], ['rgba(255,255,255,0.28)', '#FFFFFF']),
    height: 6,
    borderRadius: 6,
    marginHorizontal: 5,
  }));
  return <Animated.View style={style} />;
};

const PaginationDots = ({ data = [], activeIndex = 0 }) => {
  if (!data || data.length <= 1) return null;
  return (
    <View style={styles.paginationContainer} pointerEvents="none">
      {data.map((_, i) => <Dot key={i} active={i === activeIndex} />)}
    </View>
  );
};

// ----------------
// Posters / Sections
// ----------------
const PosterItem = React.memo(({ item, onPress, renderAs, onRemove, onInfo }) => {
  let comic = item, subtitle = null, progress = 0, hasNewChapters = false;

  if (renderAs === 'chapter') {
    comic = item.md_comics;
    subtitle = `Ch. ${item.chap}`;
  } else if (renderAs === 'progress') {
    comic = { title: item.manhwaTitle, md_covers: [item.manhwaCover], slug: item.slug };
    subtitle = `Continue Ch. ${item.lastChapterNumber}`;
    progress = item.progressPercentage || 0;
    hasNewChapters = !!item.hasNewChapters;
  }

  const [isMenuOpen, setMenuOpen] = useState(false);
  const animation = useSharedValue(0);

  const toggleMenu = (e) => {
    e.stopPropagation(); // Stop event from bubbling to parent
    setMenuOpen(prev => !prev);
  }

  useEffect(() => {
    animation.value = withTiming(isMenuOpen ? 1 : 0, {
      duration: 300,
      easing: Easing.bezier(0.33, 1, 0.68, 1),
    });
  }, [isMenuOpen]);
  
  const handleActionPress = (e, action) => {
    e.stopPropagation(); // Stop event from bubbling to parent
    if (action) action(item);
  };

  const moreIconStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${interpolate(animation.value, [0, 1], [0, 90])}deg` }]
  }));
  const actionButtonStyle = (offset) => useAnimatedStyle(() => ({
    opacity: animation.value,
    transform: [{ translateX: interpolate(animation.value, [0, 1], [0, offset]) }]
  }));

  if (!comic) return null;
  const uri = getCoverImageUrl(comic?.md_covers?.[0]) || fallbackCover;

  return (
    <Pressable style={styles.posterCard} onPress={() => onPress?.(renderAs === 'progress' ? item : comic)}>
      <View style={styles.posterImageContainer}>
        <Image source={{ uri }} style={styles.posterImage} placeholder="blurhash|L07[g_j[fQfQ00fQfQfQ00j[fQj[" />

        {renderAs === 'progress' && hasNewChapters && (
          <View style={styles.newChapterSashContainer}>
            <View style={styles.newChapterSash}><Text style={styles.newChapterSashText}>NEW</Text></View>
          </View>
        )}
        {renderAs === 'progress' && progress > 0 && (
          <View style={styles.progressStripContainer} pointerEvents="none">
            <View style={[styles.progressStrip, { width: `${progress}%` }]} />
          </View>
        )}
        
        {renderAs === 'progress' && (
          <View style={styles.actionsContainer}>
            <Animated.View style={[styles.actionButtonWrapper, actionButtonStyle(90)]}>
              <Pressable style={styles.posterActionButton} onPress={(e) => handleActionPress(e, onInfo)}>
                <Ionicons name="information-circle-outline" size={16} color="#FFF" />
              </Pressable>
            </Animated.View>
            <Animated.View style={[styles.actionButtonWrapper, actionButtonStyle(45)]}>
              <Pressable style={styles.posterActionButton} onPress={(e) => handleActionPress(e, onRemove)}>
                <Ionicons name="close" size={14} color="#FFF" />
              </Pressable>
            </Animated.View>
            <Pressable style={styles.posterActionButton} onPress={toggleMenu}>
              <Animated.View style={moreIconStyle}>
                <Ionicons name="ellipsis-vertical" size={16} color="#FFF" />
              </Animated.View>
            </Pressable>
          </View>
        )}
      </View>
      <View style={styles.posterInfo}>
        <Text style={styles.posterTitle} numberOfLines={1}>{comic?.title ?? 'Untitled'}</Text>
        {subtitle ? <Text style={styles.posterSubtitle} numberOfLines={1}>{subtitle}</Text> : null}
      </View>
    </Pressable>
  );
});

// *** FIX: Re-added the missing PosterSection component definition ***
const PosterSection = ({ title, data = [], renderAs = 'comic', onNavigate, onRemove, onInfo }) => (
  <View style={styles.sectionWrap}>
    <View style={styles.sectionHeader}>
      <Text style={styles.sectionTitle}>{title}</Text>
      <TouchableOpacity style={styles.seeAllButton} onPress={() => { /* navigate to section list if you have one */ }}>
        <Text style={styles.seeAllText}>see all</Text>
        <Ionicons name="chevron-forward" size={18} color="#6B8CFF" />
      </TouchableOpacity>
    </View>

    <FlatList
      data={data}
      horizontal
      showsHorizontalScrollIndicator={false}
      renderItem={({ item }) => (
        <PosterItem item={item} onPress={onNavigate} renderAs={renderAs} onRemove={onRemove} onInfo={onInfo} />
      )}
      keyExtractor={(item, idx) => `${renderAs}-${item?.slug || item?.hid || idx}`}
      initialNumToRender={6}
      maxToRenderPerBatch={8}
      windowSize={5}
      removeClippedSubviews
      contentContainerStyle={{ paddingHorizontal: 16 }}
      ItemSeparatorComponent={() => <View style={{ width: POSTER_GAP }} />}
      getItemLayout={(_, index) => ({ length: POSTER_CARD_WIDTH + POSTER_GAP, offset: (POSTER_CARD_WIDTH + POSTER_GAP) * index + 16, index })}
    />
  </View>
);


// ----------------
// Main Screen
// ----------------
export default function HomeScreen({ navigation }) {
  const insets = useSafeAreaInsets();
  const [featured, setFeatured] = useState([]);
  const [popular, setPopular] = useState([]);
  const [latestChapters, setLatestChapters] = useState([]);
  const [continueReading, setContinueReading] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [hasNewChapters, setHasNewChapters] = useState(false);

  // carousel parallax
  const scrollX = useSharedValue(0);
  const onCarouselScroll = useAnimatedScrollHandler({ onScroll: (e) => { scrollX.value = e.contentOffset.x; } });

  const viewabilityConfigRef = useRef({ viewAreaCoveragePercentThreshold: 50 });
  const onViewableItemsChangedRef = useRef(({ viewableItems }) => {
    if (viewableItems && viewableItems.length > 0) setActiveIndex(viewableItems[0].index ?? 0);
  });

  // ---------- data helpers ----------
  const checkForNewChapters = async (recentlyReadData) => {
    if (!recentlyReadData || recentlyReadData.length === 0) {
      setHasNewChapters(false);
      return recentlyReadData;
    }

    try {
      const enhanced = await Promise.all(recentlyReadData.map(async (p) => {
        try {
          const comicData = await comickAPI.getComicDetails(p.slug);
          if (!comicData?.comic?.hid) return { ...p, hasNewChapters: false, newChaptersCount: 0 };

          const chaptersData = await comickAPI.getComicChapters(comicData.comic.hid, { limit: 5000, lang: 'en' });
          const englishChapters = chaptersData.chapters || [];

          const uniqueChaps = new Set(englishChapters.map(c => c.chap).filter(Boolean)).size;
          const stored = p.totalChaptersOnRead || 0;
          const newCount = Math.max(0, uniqueChaps - stored);
          const hasNew = newCount > 0;

          return { ...p, hid: comicData.comic.hid, newChaptersCount: newCount, hasNewChapters: hasNew };
        } catch (err) {
          console.error('checkForNewChapters item error:', err);
          return { ...p, newChaptersCount: 0, hasNewChapters: false };
        }
      }));

      setHasNewChapters(enhanced.some(it => it.hasNewChapters));
      return enhanced;
    } catch (e) {
      console.error('checkForNewChapters error:', e);
      setHasNewChapters(false);
      return recentlyReadData;
    }
  };

  const loadData = async () => {
    try {
      if (!refreshing) setLoading(true);
      const [trendingData, latestData, recentlyReadData] = await Promise.all([
        comickAPI.getTrendingComics(),
        comickAPI.getLatestChapters({ limit: 15 }),
        storageService.getRecentlyRead(6),
      ]);

      let comicsWithFollowers = [];
      if (trendingData?.trending) Object.values(trendingData.trending).forEach(day => { if (Array.isArray(day)) comicsWithFollowers = comicsWithFollowers.concat(day); });
      const fallback = trendingData?.rank || [];
      const allComics = comicsWithFollowers.length > 0 ? comicsWithFollowers : fallback;

      setFeatured(allComics.slice(0, 4));
      setPopular(allComics.slice(4, 28));
      setLatestChapters(latestData || []);

      const enhancedContinue = await checkForNewChapters(recentlyReadData || []);
      setContinueReading(enhancedContinue || []);

    } catch (err) {
      console.error('loadData error', err);
      Alert.alert('Error', 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    const unsub = navigation.addListener('focus', () => { loadData(); });
    loadData();
    return unsub;
  }, [navigation]);

  const onRefresh = useCallback(() => { 
    setRefreshing(true); 
    loadData(); 
  }, []);

  // navigation actions
  const navigateToDetail = (comic) => {
    if (!comic) return;
    navigation.navigate('ManhwaDetail', { slug: comic.slug, hid: comic.hid, title: comic.title });
  };

  const navigateToContinueReading = async (progressItem) => {
    if (!progressItem) return;
    try {
      const comicData = await comickAPI.getComicDetails(progressItem.slug);
      if (!comicData?.comic) { Alert.alert('Error', 'Failed to load manhwa.'); return; }

      const chaptersData = await comickAPI.getComicChapters(comicData.comic.hid, { limit: 5000 });
      const englishChapters = (chaptersData.chapters || []).filter(c => c.lang === 'en');
      const lastRead = englishChapters.find(ch => ch.hid === progressItem.lastChapterHid);
      if (!lastRead) { Alert.alert('Error', 'Could not find last read chapter.'); return; }

      const manhwaInfo = { slug: comicData.comic.slug, hid: comicData.comic.id, title: comicData.comic.title, cover: comicData.comic.md_covers?.[0] };

      navigation.navigate('ChapterReader', { currentChapter: lastRead, allChapters: englishChapters, manhwaInfo });
    } catch (error) {
      console.error('navigateToContinueReading error', error);
      Alert.alert('Error', 'Failed to continue reading.');
    }
  };

  const handleRemoveFromContinueReading = async (progressItem) => {
    try {
      const success = await storageService.removeReadingProgress(progressItem.slug);
      if (success) {
        const updated = await storageService.getRecentlyRead(6);
        const enhanced = await checkForNewChapters(updated || []);
        setContinueReading(enhanced || []);
      } else Alert.alert('Error', 'Failed to remove item.');
    } catch (e) {
      console.error('remove error', e);
      Alert.alert('Error', 'Failed to remove item.');
    }
  };

  const handleNavigateToManhwaInfo = (progressItem) => { if (!progressItem) return; navigation.navigate('ManhwaDetail', { slug: progressItem.slug, hid: progressItem.hid, title: progressItem.manhwaTitle }); };

  const screenData = useMemo(() => ([
    ...(continueReading.length > 0 ? [{ type: 'continue_reading', title: 'Continue Reading', data: continueReading, renderAs: 'progress' }] : []),
    { type: 'top_mangas', title: 'Top Mangas', data: popular, renderAs: 'comic' },
    { type: 'latest_chapters', title: 'Latest Chapters', data: latestChapters, renderAs: 'chapter' },
  ]), [continueReading, popular, latestChapters]);

  if (loading && !refreshing) {
    return <LoadingScreen />;
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      <FlatList
        data={screenData}
        keyExtractor={(item) => item.type}
        showsVerticalScrollIndicator={false}
        refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor="#FFF" />}
        initialNumToRender={3}
        maxToRenderPerBatch={4}
        windowSize={7}
        removeClippedSubviews
        ListHeaderComponent={(
          <>
            <View style={{ height: height * 0.68 }}>
              <AnimatedFlatList
                data={featured}
                renderItem={({ item, index }) => (
                  <AnimatedCarouselItem item={item} index={index} scrollX={scrollX} onPress={() => navigateToDetail(item)} />
                )}
                keyExtractor={(item, index) => `featured-${item?.slug || item?.hid || index}`}
                horizontal
                pagingEnabled
                showsHorizontalScrollIndicator={false}
                onScroll={onCarouselScroll}
                scrollEventThrottle={16}
                onViewableItemsChanged={onViewableItemsChangedRef.current}
                viewabilityConfig={viewabilityConfigRef.current}
                initialNumToRender={1}
                maxToRenderPerBatch={1}
                windowSize={3}
                removeClippedSubviews
                getItemLayout={(_, index) => ({ length: width, offset: width * index, index })}
              />
            </View>
            <PaginationDots data={featured} activeIndex={activeIndex} />
          </>
        )}
        renderItem={({ item }) => (
          <PosterSection
            title={item.title}
            data={item.data}
            renderAs={item.renderAs}
            onNavigate={item.renderAs === 'progress' ? navigateToContinueReading : navigateToDetail}
            onRemove={item.renderAs === 'progress' ? handleRemoveFromContinueReading : undefined}
            onInfo={item.renderAs === 'progress' ? handleNavigateToManhwaInfo : undefined}
          />
        )}
        contentContainerStyle={{ paddingBottom: 64 }}
      />
      {/* Top Navbar */}
      <View style={[styles.topBar, { paddingTop: insets.top + 6 }]}>
          <Text style={styles.logo}>M</Text>
          <View style={styles.topBarIcons}>
              <TouchableOpacity
                style={styles.iconBg}
                onPress={() => navigation.navigate('Favorites')}
              >
                  <Ionicons name="heart" size={20} color="#FF7A7A"/>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.iconBg, styles.notificationButton]}
                onPress={() => navigation.navigate('Notifications')}
              >
                  <Ionicons name="notifications" size={20} color="#FFF"/>
                  {hasNewChapters && (
                    <View style={styles.notificationBadge}>
                      <View style={styles.notificationDot} />
                    </View>
                  )}
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.iconBg}
                onPress={() => navigation.navigate('Search')}
              >
                  <Ionicons name="search" size={20} color="#FFF"/>
              </TouchableOpacity>
              <TouchableOpacity style={styles.iconBg} onPress={() => navigation.navigate('Settings')}>
                  <Ionicons name="settings-outline" size={20} color="#FFF"/>
              </TouchableOpacity>
          </View>
      </View>
    </View>
  );
}

// ----------------
// Styles
// ----------------
const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#0F0F12' },
  center: { justifyContent: 'center', alignItems: 'center' },
  topBar: {
    position: 'absolute', top: 0, left: 0, right: 0,
    flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center',
    paddingHorizontal: 20, zIndex: 10,
  },
  logo: { fontSize: 32, fontWeight: 'bold', color: '#FFF' },
  topBarIcons: { flexDirection: 'row', alignItems: 'center' },
  iconBg: {
    width: 44, height: 44, borderRadius: 30,
    backgroundColor: 'rgba(16, 15, 15, 0.41)',
    justifyContent: 'center', alignItems: 'center', marginLeft: 8,
  },
  notificationButton: { position: 'relative' },
  notificationBadge: {
    position: 'absolute', top: 6, right: 6, width: 12, height: 12, borderRadius: 6,
    backgroundColor: '#0F0F12', justifyContent: 'center', alignItems: 'center',
  },
  notificationDot: { width: 8, height: 8, borderRadius: 4, backgroundColor: '#FF7A7A' },
  carouselCard: { width: width, height: '100%', justifyContent: 'flex-end' },
  carouselImage: { ...StyleSheet.absoluteFillObject },
  carouselGradient: { ...StyleSheet.absoluteFillObject },
  carouselInfoWrap: { paddingHorizontal: 20, paddingBottom: 72 },
  pillTag: { backgroundColor: 'rgba(255,255,255,0.04)', paddingHorizontal: 10, paddingVertical: 6, borderRadius: 20 },
  pillTagText: { color: '#BDB8E8', fontWeight: '700', fontSize: 12 },
  tagRow: { flexDirection: 'row', alignItems: 'center', marginBottom: 8 },
  genreText: { color: '#9B98A9', marginLeft: 8 },
  carouselTitle: { color: '#FFF', fontSize: 28, fontWeight: '800', marginTop: 6 },
  carouselSubtitle: { color: '#9B98A9', marginTop: 6 },
  paginationContainer: {
    position: 'absolute', top: height * 0.68 - 56, alignSelf: 'center', flexDirection: 'row', alignItems: 'center', zIndex: 30
  },
  sectionWrap: { marginTop: 26 },
  sectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 16, marginBottom: 8 },
  sectionTitle: { color: '#FFF', fontSize: 18, fontWeight: '700' },
  seeAllButton: { flexDirection: 'row', alignItems: 'center' },
  seeAllText: { color: '#6B8CFF', fontWeight: '700', marginRight: 6 },
  posterCard: { width: POSTER_CARD_WIDTH },
  posterImageContainer: {
    position: 'relative',
    backgroundColor: 'rgba(30,30,36,0.6)',
    borderRadius: 12,
    overflow: 'hidden',
  },
  posterImage: {
    width: '100%', height: Math.round(POSTER_CARD_WIDTH * 1.45),
    borderRadius: 12, backgroundColor: 'rgba(30,30,36,0.6)',
  },
  posterInfo: { marginTop: 10, paddingHorizontal: 4 },
  posterTitle: { color: '#EEEFF5', fontSize: 14, fontWeight: '700' },
  posterSubtitle: { color: '#9B98A9', fontSize: 12, marginTop: 3 },
  newChapterSashContainer: {
    position: 'absolute', top: -20, right: -20,
    width: 80, height: 80,
    alignItems: 'center', justifyContent: 'center',
    transform: [{ rotate: '45deg' }], zIndex: 2,
  },
  newChapterSash: {
    width: '100%', paddingVertical: 4,
    backgroundColor: '#6CE08A', alignItems: 'center',
  },
  newChapterSashText: {
    color: '#0F0F12', fontWeight: '800', fontSize: 11,
    letterSpacing: 0.5,
  },
  progressStripContainer: {
    position: 'absolute', bottom: 0, left: 0, right: 0, height: 6,
    backgroundColor: 'rgba(0,0,0,0.35)',
  },
  progressStrip: { height: '100%', backgroundColor: '#6CE08A' },
  actionsContainer: {
    position: 'absolute',
    top: 8,
    left: 8,
    zIndex: 10,
  },
  actionButtonWrapper: {
    position: 'absolute',
  },
  posterActionButton: {
    width: 34,
    height: 34,
    borderRadius: 17,
    backgroundColor: 'rgba(20,20,22,0.85)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});